"use client";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Form, FormControl, FormField, FormItem, FormLabel } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useForm } from "react-hook-form";
import { useRouter } from "next/navigation";

const CTASection = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const router = useRouter();

  const form = useForm({
    defaultValues: {
      fullName: "",
      email: "",
    },
  });

  function onSubmit(values: any) {
    setIsSubmitting(true);
    // Handle form submission here - e.g. API call
    console.log(values);
    setTimeout(() => {
      setIsSubmitting(false);
      form.reset();
      // Redirect to early_access page with query parameters
      const queryParams = new URLSearchParams();
      if (values.fullName) queryParams.append('fullName', values.fullName);
      if (values.email) queryParams.append('email', values.email);
      
      router.push(`/early_access?${queryParams.toString()}`);
    }, 1000);
  }

  return (
    <section id="early-access" className="py-12 sm:py-16 bg-[#F2FFF6] text-[#2E475D]">
      <div className="container mx-auto px-4 sm:px-6 text-center max-w-sm">
        <h2 className="text-xl sm:text-2xl font-bold leading-tight mb-2">Ready to Take Control of your Health?</h2>
        <p className="text-sm mb-6">Join our waitlist for early access.</p>
        
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="fullName"
              render={({ field }) => (
                <FormItem className="text-left">
                  <FormLabel className="text-sm font-medium">Full Name</FormLabel>
                  <FormControl>
                    <Input 
                      className="h-12 rounded-md border-gray-200" 
                      placeholder="" 
                      {...field} 
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem className="text-left">
                  <FormLabel className="text-sm font-medium">Email Address</FormLabel>
                  <FormControl>
                    <Input 
                      className="h-12 rounded-md border-gray-200" 
                      placeholder="" 
                      {...field} 
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            
            <Button 
              type="submit" 
              className="w-full h-12 mt-2 bg-[#6A8E99] hover:bg-[#597984] text-white font-medium rounded-md"
              disabled={isSubmitting}
            >
              {isSubmitting ? "Submitting..." : "Join Waitlist"}
            </Button>
          </form>
        </Form>
      </div>
    </section>
  );
};

export default CTASection; 