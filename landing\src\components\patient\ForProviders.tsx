"use client";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import Link from "next/link";

const ForProvidersSection = () => {
  const features = [
    "Help patients track symptoms and medication more effectively",
    "Gain useful insight from their AI assisted logs",
    "Recommend Petals AI as a companion app for improved compliance"
  ];

  return (
    <section className="py-12 sm:py-16 md:py-20 lg:py-24 bg-[#F2FFF6]">
      <div className="container mx-auto px-4 sm:px-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
          {/* Image shown on desktop only */}
          <div className="hidden lg:block relative h-[500px] lg:px-[-32px]">
            <Image
              src="/images/provider_image.svg"
              alt="Petals Health Platform"
              fill
              style={{ objectFit: 'contain' }}
              priority
            />
          </div>
          <div className="flex flex-col">

            {/* Image shown on mobile only after heading */}
            <div className="block lg:hidden relative h-[300px] mb-8">
              <Image
                src="/images/provider_image.svg"
                alt="Petals Health Platform"
                fill
                style={{ objectFit: 'contain' }}
                priority
              />
            </div>
            <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-[#2E475D] mb-4">For Providers</h2>

            <h3 className="text-lg sm:text-xl font-semibold text-[#2E475D] mb-4">
              Support Your Patient with Smart Tools
            </h3>

            <div className="flex flex-col gap-4 mb-8">
              {features.map((feature, index) => (
                <div key={index} className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-[#6A8E99] rounded-full mt-2 flex-shrink-0"></div>
                  <p className="text-sm sm:text-base text-gray-700">{feature}</p>
                </div>
              ))}
            </div>

            <Link href="/early_access">
              <Button
                variant="secondary"
                className="bg-[#6A8E99] hover:bg-[#5a7a84] text-white px-8 py-3 rounded-md h-[56px] w-[247px] mx-auto sm:mx-0"
              >
                Explore Provider Tools
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ForProvidersSection; 