"use client";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import Link from "next/link";

const ForProvidersSection = () => {
  const features = [
    {
      title: 'Understand your health in real-time using AI built on clinical data.',
      width: 'w-[40%]',
    },
    {
      title: 'Optimize your medication and supplements with intelligent recommendations',
      width: 'w-[60%]',
    },
    {
      title: 'Track symptoms, health patterns and treatment progress- all in one dashboard',
      width: 'w-[60%]',
    },
    {
      title: 'HIPAA and GDPR-compliant Privacy & Security',
      width: 'w-[40%]',
    },
  ];

  return (
    <section className="py-12 sm:py-16 md:py-20 lg:py-24 bg-[#F2FFF6]">
      <div className="container mx-auto px-4 sm:px-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
          {/* Image shown on desktop only */}
          <div className="hidden lg:block relative h-[500px] lg:px-[-32px]">
            <Image
              src="/images/provider_image.svg"
              alt="Petals Health Platform"
              fill
              style={{ objectFit: 'contain' }}
              priority
            />
          </div>
          <div className="flex flex-col">

            {/* Image shown on mobile only after heading */}
            <div className="block lg:hidden relative h-[300px] mb-8">
              <Image
                src="/images/provider_image.svg"
                alt="Petals Health Platform"
                fill
                style={{ objectFit: 'contain' }}
                priority
              />
            </div>
            <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-[#2E475D] mb-4">What is petals Health AI?</h2>

            <p className="text-base sm:text-lg text-gray-600 mb-8">
              Petals Health is a first-of-its-kind AI + telehealth platform that help you
            </p>

            <div className="flex flex-col gap-6 mb-8">
              <div className="flex flex-col sm:flex-row gap-6 w-full">
                <Card className="shadow-md border-0 overflow-hidden rounded-lg w-full sm:w-[40%]">
                  <CardContent className="p-6">
                    <p className="text-sm sm:text-base text-gray-700">{features[0].title}</p>
                  </CardContent>
                </Card>

                <Card className="shadow-md border-0 overflow-hidden rounded-lg w-full sm:w-[60%]">
                  <CardContent className="p-6">
                    <p className="text-sm sm:text-base text-gray-700">{features[1].title}</p>
                  </CardContent>
                </Card>
              </div>

              <div className="flex flex-col sm:flex-row gap-6 w-full">
                <Card className="shadow-md border-0 overflow-hidden rounded-lg w-full sm:w-[60%]">
                  <CardContent className="p-6">
                    <p className="text-sm sm:text-base text-gray-700">{features[2].title}</p>
                  </CardContent>
                </Card>

                <Card className="shadow-md border-0 overflow-hidden rounded-lg w-full sm:w-[40%]">
                  <CardContent className="p-6">
                    <p className="text-sm sm:text-base text-gray-700">{features[3].title}</p>
                  </CardContent>
                </Card>
              </div>
            </div>

            <Link href="/early_access">
              <Button
                variant="secondary"
                className="bg-[#6A8E99] hover:bg-[#5a7a84] text-white px-8 py-3 rounded-md h-[56px] w-[247px] mx-auto sm:mx-0"
              >
                Join Waitlist
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ForProvidersSection; 