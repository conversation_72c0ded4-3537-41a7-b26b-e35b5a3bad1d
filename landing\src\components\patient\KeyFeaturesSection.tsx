"use client";
import { TestTube, <PERSON>, Plus } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";

const KeyFeaturesSection = () => {
  const features = [
    {
      icon: <TestTube className="h-12 w-12 text-gray-400" />,
      title: "Symptom Contextualizer",
      description: "Understand symptoms with real-time, AI-powered context and suggestions"
    },
    {
      icon: <Brain className="h-12 w-12 text-gray-400" />,
      title: "Pain & Fatigue AI Companion",
      description: "Daily check-ins to help manage chronic discomfort and track patterns"
    },
    {
      icon: <Plus className="h-12 w-12 text-gray-400" />,
      title: "Medication & Supplier",
      description: "AI insights to optimize treatment plans and enhance safety"
    }
  ];

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Features
          </h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {features.map((feature, index) => (
            <Card key={index} className="bg-gray-50 border-0 shadow-none hover:shadow-md transition-shadow duration-300">
              <CardContent className="p-8 text-left">
                <div className="mb-6">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">
                  {feature.title}
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  {feature.description}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default KeyFeaturesSection; 