"use client";
import Image from "next/image";
import { Card, CardContent } from "@/components/ui/card";
import Link from "next/link";

const TargetUsersSection = () => {
  const targetUsers = [
    {
      title: "Chronic illness Warrior",
      description: "People managing diabetes, hypertension, autoimmune conditions, or other chronic health challenges.",
      imageSrc: "images/001Carro.svg", // Placeholder until real image is available
      alt: "Patient in hospital bed"
    },
    {
      title: "Medication Managers",
      description: "Users with complex medication regimens or those tracking symptoms to optimize their health.",
      imageSrc: "images/002Carro.svg", // Placeholder until real image is available
      alt: "Hand holding medications"
    },
    {
      title: "Health Researchers",
      description: "Anyone tired of Googling symptoms and wanting reliable, personalized health information.",
      imageSrc: "images/003Carro.svg", // Placeholder until real image is available
      alt: "Healthcare professional with medical equipment"
    },
    {
      title: "Busy Professionals",
      description: "People needing expert healthcare guidance that fits their demanding schedule.",
      imageSrc: "images/004Carro.svg", // Placeholder until real image is available
      alt: "Professionals working at a desk"
    }
  ];

  return (
    <section className="py-12 bg-[#F2FFF6] sm:py-16 md:py-20">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-8 md:mb-12">
          <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-[#2E475D]">
            Who is Petals Health AI for
          </h2>
        </div>

        {/* Mobile view - Vertical cards with smaller gaps */}
        <div className="md:hidden space-y-4">
          {targetUsers.map((user, index) => (
            <Card key={index} className="overflow-hidden border-none shadow-sm bg-white rounded-xl p-0">
              <div className="relative w-full h-48">
                <Image
                  src={user.imageSrc}
                  alt={user.alt}
                  width={600}
                  height={400}
                  className="w-full h-full object-cover rounded-t-xl"
                />
              </div>
              <CardContent className="p-5">
                <h3 className="text-lg font-semibold text-[#2E475D] mb-1">{user.title}</h3>
                <p className="text-sm text-gray-600 mb-2">{user.description}</p>
                <Link href="/early_access" className="text-sm text-blue-500 hover:text-blue-700 font-medium">
                  Read more
                </Link>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Tablet view - 2 columns */}
        <div className="hidden md:grid lg:hidden md:grid-cols-2 gap-6">
          {targetUsers.map((user, index) => (
            <Card key={index} className="overflow-hidden border-none shadow-sm bg-white rounded-xl h-full flex flex-col p-0">
              <div className="relative w-full h-48">
                <Image
                  src={user.imageSrc}
                  alt={user.alt}
                  width={600}
                  height={400}
                  className="w-full h-full object-cover rounded-t-xl"
                />
              </div>
              <CardContent className="p-5 flex flex-col flex-grow">
                <h3 className="text-lg font-semibold text-[#2E475D] mb-1">{user.title}</h3>
                <p className="text-sm text-gray-600 mb-2 flex-grow">{user.description}</p>
                <Link href="/early_access" className="text-sm text-blue-500 hover:text-blue-700 font-medium">
                  Read more
                </Link>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Desktop view - 4 columns */}
        <div className="hidden lg:grid lg:grid-cols-4 gap-6">
          {targetUsers.map((user, index) => (
            <Card key={index} className="overflow-hidden border-none shadow-sm bg-white rounded-xl h-full flex flex-col p-0">
              <div className="relative w-full h-48">
                <Image
                  src={user.imageSrc}
                  alt={user.alt}
                  width={600}
                  height={400}
                  className="w-full h-full object-cover rounded-t-xl"
                />
              </div>
              <CardContent className="p-5 flex flex-col flex-grow">
                <h3 className="text-lg font-semibold text-[#2E475D] mb-1">{user.title}</h3>
                <p className="text-sm text-gray-600 mb-2 flex-grow">{user.description}</p>
                <Link href="/early_access" className="text-sm text-blue-500 hover:text-blue-700 font-medium">
                  Read more
                </Link>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default TargetUsersSection;
